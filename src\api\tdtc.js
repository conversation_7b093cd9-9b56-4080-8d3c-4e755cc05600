import axios from "axios";
import i18n from "@/lang";
import store from "@/store";
import {getAuthUrl, getQueryInfoUrl, getRechargeUrl, getThirdGameUrl} from "@/utils/common";
import router from '@/router'
import Vue from 'vue'

export const TDTC_ROURE = {
    QUERY_BET                     : "1",  //游戏下注信息
    QUERY_MONEY_INFO              : "2",  //资金明细
    QUERY_AGENT_NEXT_INFO         : "3",  //直属下级明细
    QUERY_AGENT_SPREAD_INFO_A     : "4",  //推广任务信息 上面
    QUERY_AGENT_SPREAD_INFO_B     : "5",  //推广任务信息 下面
    QUERY_AGENT_INFO              : "6",  //代理首页信息
    QUERY_AGENT_REPORT            : "7",  //代理报表
    QUERY_AGENT_NEXT_REPORT       : "8",  //代理下级报表
    QUERY_AGENT_DRAW              : "9",  //提取返现记录
    QUERY_GROW_FUND               : "10", //成长基金
    QUERY_AD_GROW_FUND            : "11", //成长基金广告
    QUERY_NEW_PEOPLE_AWARD        : "12", //新人礼包
    QUERY_RECHARGE_SIGN           : "13", //充值签到
    QUERY_AD_AGENT_SPREAD_INFO_A  : "14", //广告 推广任务信息 上面
    QUERY_TUENTABLE_INFO          : "15", //转盘
    QUERY_TUENTABLE_RECORDS       : "16", //转盘记录
    // QUERY_ONLINE_INFO             : "17", //在线信息
    QUERY_DAILY_TASK_INFO         : "18", //每日任务
    QUERY_DAILY_RESCUE_INFO       : "19", //每日救援
    QUERY_REDPACK_INFO            : "20", //红包雨
    QUERY_REDPACK_LOG             : "21", //红包雨记录
    QUERY_VIP_ACTIVE_INFO         : "22", //VIP信息
    QUERY_AD_VIP_ACTIVE_INFO      : "23", //广告 VIP信息
    QUERY_DAILY_RECHARGE_INFO     : "24", //每日首存
    QUERY_BONUS_INFO              : "25", //BONUS
    QUERY_BETAWARD_INFO           : "26", //流水奖励
    QUERY_EXCHANGE_BIND_INFO      : "27", //查询兑换绑定
    QUERY_EXCHANGE_RECORD         : "28", //获取兑换记录
    QUERY_EMAIL                   : "29", //请求邮件
    QUERY_CHECK_EMAIL             : "30", //查看邮件
    QUERY_GROW_LEVEL              : "31", //查询经验等级
    QUERY_CRYPTO_RATE             : "32", //虚拟币汇率
    QUERY_TOTAL_RECHARGE          : "33", //累计充值奖励信息
    QUERY_AD_TOTAL_RECHARGE       : "34", //广告 累计充值奖励信息
    QUERY_AD_EARNCASH_BY_PROMOTE  : "35", //广告 推广得现金
    QUERY_EARNCASH_BY_PROMOTE_LOG : "36", //推广得现金 提现记录
    QUERY_AGENT_DAILY_RANK_CONF   : "37", //查询代理每日排行榜配置
    QUERY_AGENT_DAILY_RANK        : "38", //代理每日排行榜---查询排行榜信息
    QUERY_USER_SCORE_INFO         : "39", //获取玩家金币数据
    QUERY_SOCIAL_INFO             : "40", //获取社交绑定信息
    QUERY_WITHDRAW_CONDITION      : "41", //获取提现条件是否已经达成

    QUERY_ACTIVE_POPUP                      : "46", //活动弹窗(登录成功后请求一次)
    QUERY_SUBSIDY_DETAILS                   : "47", //补助金活动详情
    GET_AGENT_RANK_AWARD_SUBSIDY_RECEIVE    : "48", //领取补助金
    QUERY_FD_CHECK_DETAILS                  : "49", //签到现金奖金详情
    GET_FD_CHECK                            : "50", //请求签到

    QUERY_ACTIVE200_URL    : "200", //活动按钮里 分享奖励  网页地址
    QUERY_ACTIVE201_URL    : "201", //活动按钮里 见面礼 从其他平台转来的奖励 网页地址
    QUERY_ACTIVE200_URL_Ad : "202", //广告页面  分享奖励  网页地址
    QUERY_ACTIVE201_URL_Ad : "203", //广告页面  见面礼 从其他平台转来的奖励 网页地址
    QUERY_RECHARGE_GIFT    : "204", //每周活动 - 接收超级礼物
    QUERY_WALLET_NAME      : "205", //绑定钱包配置信息
    QUERY_ACTIVE203_URL_Ad : "206", //广告页面  充值优惠  网页地址
    // QUERY_BOSS_AGENT_INFO  : "210", //BOSS代理首页信息

    QUERY_AGENT_REBATE_RANK : "300", //代理返利排行榜
    QUERY_AGENT_SPREAD_RANK : "301", //推广任务排行榜
    QUERY_WIN_SCORE_RANK    : "302", //请求赢分排行榜
    QUERY_ONLINE_TIME_RANK  : "303", //在线时长排行榜
    QUERY_GROW_LEVEL_RANK   : "304", //等级排行榜



    GET_DAILY_RESCUE                   : "1001", //每日救援
    GET_DAILY_TASK_SCORE               : "1002", //每日任务积分
    GET_DAILY_TASK_MONEY               : "1003", //每日任务金币宝箱
    GET_GROW_FUND                      : "1004", //成长基金
    GET_NEW_PEOPLE_AWARD               : "1005", //新人礼包
    GET_RECHARGE_SIGN                  : "1006", //充值签到
    GET_TUENTABLE                      : "1007", //转盘
    GET_REDPACK_RAIN                   : "1008", //红包雨
    GET_DAILY_RECHARGE                 : "1009", //每日首存
    GET_BET_AWARD                      : "1010", //流水奖励
    GET_EXCHANGE_CARD                  : "1011", //礼包兑换码
    // GET_ONLINE_AWARD                   : "1012", //在线时长领积分
    GET_VIP_UP_AWARD                   : "1013", //领取vip晋级礼金
    GET_VIP_UP_WAGE                    : "1014", //领取vip俸禄
    GET_TOTAL_RECHARGE                 : "1015", //累计充值奖励
    GET_AGENT_RANK_AWARD               : "1016", //代理排行榜奖励
    GET_EARNCASH_BY_PROMOTE_DETAIL     : "1017", //获取[推广得现金]活动详情
    GET_EARNCASH_BY_PROMOTE_LOTTERY    : "1018", //推广得现金---转盘抽奖
    GET_EARNCASH_BY_PROMOTE_WITHDRAWAL : "1019", //推广得现金---确认提现
    GET_AGENT_SPREAD_AWARD             : "1020", //领取代理推广优质玩家奖励
    HALL_MODIFY_NAME                   : "1021", //修改昵称
    HALL_MODIFY_FACE                   : "1022", //更换头像和头像框
    HALL_BIND_EXCHANGE                 : "1023", //兑换账号绑定
    HALL_APPLY_EXCHANGE                : "1024", //申请兑换
    GET_AGENT_DRAW                     : "1025",
    HALL_BIND_SOCIAL                   : "1026", //社交信息绑定
    SET_WITHDRAW_PASSWD                : "1027", //设置提现密码

}

const instance = axios.create();

instance.interceptors.response.use(
    function (response) {
        setTimeout(()=>{
            window.$toast.clear()
        }, 2000)
        iconsole(response.config.url, response.data)
      let data = response.data;
        if (response.config.params && response.config.params.querytype && response.config.params.querytype === '1025' && data.code === 506) {
            window.$toast.fail("Tài khoản này đã được đăng nhập ở thiết bị khác!");
            return Promise.reject(data);
        }
        switch (data.code) {
            case 506:
                store.commit("setLogout");
                store.commit("initConfig");
                if (store.state.webType > 1) {
                    setTimeout(()=> {
                        router.push("/m/login").catch(()=>{})
                    }, 2000)
                } else {
                    setTimeout(()=> {
                        Vue.prototype.$modal.show('loginPopupModal')
                    }, 2000)
                }
                break;
        }
      if (data.code && data.code > 200 && data.code < 1000 && ![
          400,
          587,
          588,
          589,
          590,
          591,
          592,
          593].includes(data.code)) {
        let msg = i18n.t(data.code);
        window.$toast.fail(msg);
        return Promise.reject(data);
      }
      return Promise.resolve(data);
    },
    function (error) {
      iconsole("===err");
      iconsole(error);
      return Promise.reject(error);
    }
);

export default {
    test: function () {
        return instance.request({
            method: 'post',
            url:    'http://192.168.8.128:8000/test',
        })
    },
    getAuth: function (config) {
        config['baseURL'] = getAuthUrl();
        return instance.request(config)
    },
    getRecharge: function (config) {
        config['baseURL'] = getRechargeUrl();
        // config['params']['signature'] = "524656dbe1be40859aa9bd4ce2e628a83d1c49c2d8d5405e8f15d2199b8b1e96"
        return instance.request(config)
    },
    getThirdGame: function (route, params = {}) {
        if (route === "enterGame") {
            window.$toast.loading({
                message: i18n.t('a6.common.tips.loadingTip'),
                forbidClick: true,
                duration: 6000
            });
        }
        let ext = {
            userid: store.state.account.userId,
            time: new Date().getTime(),
            signature: store.state.token.token,
            // signature: "524656dbe1be40859aa9bd4ce2e628a83d1c49c2d8d5405e8f15d2199b8b1e96",
        }
        Object.assign(params, ext)
        return instance.get(route, {
            baseURL: getThirdGameUrl(),
            params: params
        })
    },
    getQueryInfo: function (route, data = {}) {
        data.token = store.state.token.token
        // data.token = "524656dbe1be40859aa9bd4ce2e628a83d1c49c2d8d5405e8f15d2199b8b1e96"
        return instance.post(getQueryInfoUrl(), data, {
            params: {
                querytype: route
            }
        })
    }
}
