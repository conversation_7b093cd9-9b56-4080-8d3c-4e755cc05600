<script>
import {menu} from "@/mixins/menu";

export default {
  name: "AppGameCategoryV",
  mixins: [menu],
  data() {
    return {
      swiperOptions: {
        direction: 'vertical',
        slidesPerView: "auto",
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        mousewheel: {
          forceToAxis: true, // 强制滚轮方向与切换方向一致
        }
      },
    }
  }
}
</script>

<template>
  <section style="position: relative">
    <swiper class="AppGameCategoryV" :options="swiperOptions" style="height: calc(100vh - 6.7rem);padding-top: .2rem;">
      <template v-for="(item, index) in menuOptions">
      <swiper-slide
          v-if="index < 0 && item.show"
          :key="index"
      >
        <div class="swiper-item" @click="setCategory(index); index === '-2' && loadGame()" :class="{
                      on: $store.state.platform.currentCategory === index,
                    }">
          <svg class="am-icon am-icon-icon-home am-icon-md"><use v-bind:xlink:href="`#${item.icon}`"></use></svg>
          <span>{{item.title }}</span>
        </div>
      </swiper-slide>
      </template>
      <swiper-slide
          v-for="item in $store.state.platform.categories"
          :key="item.categoryId"
      >
        <div class="swiper-item" @click="setCategory(item.categoryId)" :class="{
                    on:
                      $store.state.platform.currentCategory === item.categoryId,
                  }">
          <svg class="am-icon"><use v-bind:xlink:href="`#${menuOptions[item.categoryId].icon}`"></use></svg>
          <span>{{ menuOptions[item.categoryId].title }}</span>
        </div>
      </swiper-slide>

      <swiper-slide
          :key="-4"
      >
        <div @click="setCategory('-4'); $store.commit('setPlatformPlatform', 0);loadGame()" :class="{
                      on: $store.state.platform.currentCategory === '-4',
                    }"
            class="swiper-item">
          <svg class="am-icon"><use v-bind:xlink:href="`#${menuOptions['-4'].icon}`"></use></svg>
          <span>{{ menuOptions['-4'].title }}</span>
        </div>
      </swiper-slide>
    </swiper>
<!--    <van-icon name="arrow" class="swiper-button-next" color="#6a6a6a"/>-->
<!--    <van-icon name="arrow-left" class="swiper-button-prev" color="#6a6a6a"/>-->
  </section>
</template>

<style scoped lang="scss">
.swiper-slide{
  height: unset;
}
.swiper-item {
  height: 1.16rem;
  display         : flex;
  flex-direction  : column;
  justify-content : center;
  align-items     : center;
  font-weight: 400;
  font-size: 0.19rem;
  color: #705840;
  background-size: contain;
  background-repeat: no-repeat;
  svg {
    height : .38rem;
    margin-bottom: .2rem;
    fill: #705840
  }
}

.swiper-item.on {
  color: rgb(255 182 39 / 1);
  svg {
    fill: rgb(255 182 39 / 1);
  }
}
</style>