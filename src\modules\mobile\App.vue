<template>
  <div>
    <div style="position: fixed; height: 100vh;top: 0; width: var(--theme-max-width);z-index: 20002" v-show="loading">
      <img style="height: 100%; width: 100%" src="img/login-img.jpg" alt="">
    </div>
    <span id="root">
      <div data-reactroot="">
        <div>
          <div id="mc-animate-container" class="v-base v-detail-base">
            <div
              :class="{'_container_box': !['/m/inviteFriends'].includes($route.path)}"
            >
              <div id="page_bg" class="common" v-if="hasBg"></div>
<!--              <show-bar-->
<!--                  v-if="-->
<!--                  ['/m', '/m/', '/m/index.html'].includes($route.path)-->
<!--                "-->
<!--              />-->
              <home-header
                  v-if="
                  ['/', '/m', '/m/', '/m/index.html'].includes($route.path)
                "
              />
              <router-view :key="mainRouter<PERSON>ey" v-cloak/>
              <tabbar
                v-if="
                  ['/', '/m', '/m/', '/m/index.html', '/m/member/home', '/m/inviteFriends'].includes($route.path)
                "
              />
<!--              <sidebar />-->
            </div>
          </div>
        </div>
        <div>
          <div class="notification-container notification-container-empty">
            <div></div>
          </div>
        </div>
      </div>
    </span>
    <choose-game v-if="$store.state.mobile.chooseGame.show"/>
<!--    <EarnWheel v-if="$store.state.activity15Step === 1" v-cloak/>-->
    <Ad />
    <bottom-modal v-if="$store.state.bottomModalStatus === 1"/>
    <van-popup @close="closePopup" v-model="$store.state.showBannersNotice" v-if="bannersNotice.length" round closeable style="width: 93%; height: 80%; background-color:#1b2132">
      <div style="height: 1rem; background-color: #181f2b;font-size: .4rem;text-align: center;line-height: 1rem;font-weight: 600;color: white;">{{ $t('popularize') }}</div>
      <div style="height: 90%;overflow: hidden;">
        <div class="activity_popup" style="height: 100%;overflow: scroll;padding: 0.24rem 0.2rem 0 0.2rem">
          <div class="promo_item" v-for="(item, index) in bannersNotice" :key="index"
              @click="$store.commit('setShowBannersNotice', false); goDetail(item.id); event_bannerClick(item.id)">
            <div class="activity">
              <div class="activity_img">
                <img
                    :src="item.imageUrl"
                    :alt="item.title"
                    style="object-fit: fill"
                />
              </div>
              <div class="activity_info">
                <div class="item-title">{{ item.title }}</div>
                <svg class="am-icon am-icon-icon-more am-icon-md icon-more">
                  <use xlink:href="#icon-more"></use>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
    <van-popup v-model="ac" lock-scroll>内容</van-popup>
  </div>
</template>
<script>
import showBar from "@/modules/mobile/components/show-bar.vue";
import HomePage from "@/modules/mobile/views/index/index.vue";
import HomeHeader from "@/modules/mobile/components/home-header.vue";
// import Sidebar from "@/modules/mobile/components/sidebar.vue";
import tabbar from "@/modules/mobile/components/menu.vue";
import { logon } from "@/mixins/logon";
import ChooseGame from "@/modules/mobile/components/popup/chooseGame.vue";
import {init} from "@/mixins/init";
import EarnWheel from "@/modules/mobile/components/EarnWheel.vue";
import {lang} from "@/mixins/lang";
import Ad from "@/modules/mobile/components/popup/Ad.vue";
import BottomModal from '@/modules/mobile/components/BottomModal.vue'

export default {
  components: {
    BottomModal,
    Ad,
    EarnWheel,
    ChooseGame,
    tabbar,
    // Sidebar,
    HomeHeader,
    HomePage,
    showBar,
  },
  mixins: [init, logon, lang],
  data() {
    return {
      key: null,
      loading: true,
      ac:true
    };
  },
  watch: {
    $route(to, form) {
      this.key = Date.now();
    },
  },
  computed: {
    hasBg() {
      return !this.$route.path.startsWith('/m/events/')
    }
  },
  beforeMount() {
    this.$store.commit("setDevice", 2);
    this.logonInnit();
  },
  mounted() {
    setTimeout(()=>{
      this.loading = false
    }, 800)
  },
  methods: {
    closePopup() {
      this.$store.commit('setShowBannersNotice', false);
    }
  }
};
</script>

<style lang="scss">
:root {
  --bs-body-bg: #F9F9F9 !important;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}



.showqr .mc-canvas-wrap {
  //position : unset !important;
}

.showqr .van-dialog__message {
  padding : 0;
}

.showqr.van-dialog {
  background-color : rgb(0, 0, 0, 0);
  width: 5.4rem;
}



.rotateFull {
  -webkit-animation: rotate-full .5s linear infinite;
  animation: rotate-full .5s linear infinite;
}

.close-eye {
  color: rgb(165, 165, 165);
}
.open-eye {
  color: rgb(76, 132, 248);;
}


input {
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  user-select: auto !important;
}

.sb-disable {
  background: #D5D5D5 !important;
  color: #939393 !important;
}

.van-tab__pane {
  height: calc(100vh - 3.3rem - var(--safe-area-inset-bottom));
  overflow: scroll;
  background: #F9F9F9;
}

.activity_popup .promo_item {
  position: relative;
  width: 100%;
  margin-bottom: .24rem;
  border-radius: .15rem;
  background-image: linear-gradient(93deg, #ce392f 14%, #451c9d 83%)
}

.activity_popup .promo_item .activity_type {
  position: absolute;
  top: .04rem;
  left: -.08rem;
  z-index: 99;
  height: .98rem
}

.activity_popup .promo_item .activity_img {
  width: 100%;
  overflow: hidden;
  //border-radius: .12rem .12rem 0 0;
  border-radius: .12rem;
  position: relative
}

.activity_popup .promo_item .activity_img img {
  display: block;
  width: 100%;
  min-height: 2rem
}

.activity_popup .promo_item .activity_info {
  height: .68rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 .139rem 0 .18rem
}

.activity_popup .promo_item .activity_info .item-title {
  font-size: .28rem;
  font-weight: 600;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.activity_popup .promo_item .activity_info .icon-more {
  width: 1.281rem;
  height: .51rem
}

.activity_popup .promo_item .activity_info .promo-end-date {
  display: flex;
  justify-content: space-between;
  margin-top: .15rem
}

.activity_popup .promo_item .activity_info .promo-end-date .section-count.day {
  margin-right: .53rem
}

.activity_popup .promo_item .activity_info .promo-end-date .promo-no-expiry {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.8rem;
  height: .63rem;
  border-radius: .1rem;
  font-size: .26rem;
  font-weight: 900;
  color: #fff;
  text-align: center;
  background-color: #4b4b8b
}

.activity_popup .promo_item .activity_info .more-btn {
  min-width: 1.69rem;
  height: .63rem;
  line-height: .63rem;
  padding: 0 .2rem;
  border-radius: .2rem;
  font-size: .24rem;
  font-weight: 900;
  color: #000;
  text-align: center;
  box-shadow: inset 0 0 .06rem 0 #ffb206;
  background-image: linear-gradient(180deg, #fff79d, #ffd800)
}

.activity_popup .promo_item .activity_info .promo-down {
  width: .5rem;
  height: .48rem;
  transition: all .2s;
  color: #fff
}

.activity_popup .promo_item .activity_info .promo-down svg {
  display: block;
  width: 100%;
  height: 100%
}

.ant-form .ant-input:hover {
  border-color: #FFB627;
  border-right-width: 1px !important;
}
.ant-form .ant-input:focus {
  border-color: #FFB627;
  border-right-width: 1px !important;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.ant-form .ant-input:focus {
  box-shadow: 0 0 0 2px rgba(255, 182, 39, .2);
}

.van-tabs__nav--card {
  border: none;
}
.van-tabs__nav--card .van-tab {
  border-right : none;
}
.van-tabs__nav--card .van-tab.van-tab--active {
  border-radius: .08rem;
}
button.van-button--disabled {
  opacity: 1 !important;
  background: #D5D5D5 !important;
  border:none !important;
  .van-button__text {
    color: #8C8C8C !important;
  }
}
.van-button {
  height: .68rem !important;
  border-radius: 0.1rem !important;
  margin: 0.2rem auto !important;
}
#app .van-button__text {
  font-weight: 600 !important;
  font-size: 0.26rem !important;
  color: #FFFFFF !important;
}

.sigin-content {
  background: #F9F9F9;
  border-top-left-radius: 0.18rem;
  border-top-right-radius: 0.18rem;
  margin-top: -.15rem;

  .sigin-c-footer, .sigin-c-remarks {
    margin: .2rem;
  }

  .sigin-c-remarks {
    background: #F0DEDC;
    border-radius: 0.08rem;
    border: 0.02px solid #E6D3D7;
    font-size: 0.23rem;
    color: #B24048;
    padding: .1rem;
  }
}

#mc-header, #menu, .van-popup {
  max-width: var(--theme-max-width);
}

.van-popup--bottom {
  left: auto !important;
}

.van-button--default {
   border: none !important;
}

.van-tab {
  font-size: .26rem;
}
.van-tabs--card>.van-tabs__wrap,
.van-tabs--line .van-tabs__wrap,
.van-tabs__nav--card {
  height: 0.6rem;
  //line-height: 0.6rem;
}
.van-tabs__nav, .van-cell-group, .van-cell{
  background-color: unset !important;
}
.ant-modal-mask {
  background-color: rgba(1, 1, 1, 1) !important;
}
.van-picker__confirm {
  color: #FFB627 !important;
}
</style>