<script>
import { type53 } from "@/mixins/tdtc/events/type53";

export default {
  mixins: [type53],
  methods: {
    select(date) {
      this.selectDay = date.getDate();
    },
    formatter(day) {
      const month = day.date.getMonth() + 1;
      const date = day.date.getDate();
      if (month === this.res.nowmonth) {
        this.res.info.forEach((row) => {
          if (row['signday'] === date) {
            day.bottomInfo = 'signed';
          }
        })
      }
      if (day.type === 'selected') {
        if (day.bottomInfo === 'signed') {
          this.signed = true
        } else {
          this.signed = false
        }
      }
      return day;
    },
  },

}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-signIn am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use>
              </svg></span></span>
        </div>
        <div class="am-navbar-title" style="font-size: .32rem !important;">{{ $t(`events_page.type.53`) }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div
      style="margin-bottom: 0.5rem;background-image: url('img/activity/19/bg.png');background-size: contain;background-repeat: no-repeat;width: 100%;min-height: 10.6rem;">
      <div class="light_box">
        <div v-for="index in 7" :key="index">
          <span>Ngày thứ {{ index }}</span>
          <img src="img/activity/19/on.png" alt="">
        </div>
      </div>
      <div>
        
      </div>
      <div class="light_action">
        <div>Tien thuong</div>
        <div>Tien thuong</div>
      </div>
      <div
        style="padding-top:.2rem;margin-top: .4rem;height: calc(100vh - 7.9rem);background: linear-gradient(180deg, #EEE5FF, #FFFFFF);"
        class="sigin-content">
        <div style="width: 7.5rem;
height: 0.43rem;
background: #F7BC88;display: flex;justify-content: center;color: #7C290B;">Quy tắc & Hướng dẫn</div>
        <div style="font-size: 0.23rem;
color: #7D1000;
padding: .2rem;">
          <p>1. Người chơi phải đăng nhập liên tục 5 ngày mới được nhận thưởng; nếu bỏ lỡ bất kỳ ngày nào thì sẽ reset
            lại
            từ đầu, không thể bù ngày.</p>
          <p> 2. Tiền thưởng có giới hạn số lượng – ai đăng nhập đủ 5 ngày sớm nhất từ 00:00 ngày thứ 5 sẽ nhận được
            thưởng.</p>
          <p> 3. Để tham gia nhận thưởng đăng nhập, bạn cần hoàn tất đăng ký và liên kết số điện thoại. Nếu không liên
            kết
            số điện thoại, bạn sẽ không thể nhận thưởng!</p>
        </div>
        
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>

      </div>
    </div>
  </section>
</template>
<style scoped>
.finished {
  background-image: linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}

.light_box {
  position: relative;
  height: 5.8rem;

  div {
    font-size: 0.16rem;
    color: #FFFFFF;
    display: flex;
    flex-direction: column;
    position: absolute;

    span {
      width: 0.93rem;
      height: 0.3rem;
      background: #B46656;
      border-radius: 0.06rem;
      margin-bottom: .2rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    img {
      width: 0.83rem;
      height: 1.35rem;
      box-shadow: 0rem 0rem 0rem 0rem rgba(0, 0, 0, 0.15);
    }
  }

  div:nth-child(1) {
    top: 1.45rem;
    left: 0.1rem;
  }

  div:nth-child(2) {
    top: 2.36rem;
    left: 1.8rem;
  }

  div:nth-child(3) {
    top: 1.5rem;
    left: 2.8rem;
  }

  div:nth-child(4) {
    top: 1.26rem;
    right: 1.8rem;
  }

  div:nth-child(5) {

    top: 0.53rem;
    right: 0.1rem;
  }

  div:nth-child(6) {
    top: 3.3rem;
    right: 0.4rem;
  }

  div:nth-child(7) {
    top: 3rem;
    left: 4rem;
  }
}

.light_action {
  display: flex;
  justify-content: space-around;
  align-items: center;
  color: #FFFFFF;
  padding: 0 .3rem;

  div {
    width: 3.09rem;
    height: 0.64rem;
    border-radius: 0.1rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  div:nth-child(1) {
    background: linear-gradient(90deg, #EC833A, #F9B606);
  }

  div:nth-child(2) {
    background: linear-gradient(90deg, #D31176, #F471DD);
  }
}
</style>