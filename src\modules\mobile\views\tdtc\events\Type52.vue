
<script>
import {type52} from "@/mixins/tdtc/events/type52";

export default {
  mixins: [type52],
  methods: {
    select(date) {
      this.selectDay = date.getDate();
    },
    formatter(day) {
      const month = day.date.getMonth() + 1;
      const date = day.date.getDate();
      if (month === this.res.nowmonth) {
        this.res.info.forEach((row)=>{
          if (row['signday'] === date) {
            day.bottomInfo = 'signed';
          }
        })
      }
      if (day.type === 'selected') {
        if (day.bottomInfo === 'signed') {
          this.signed = true
        } else {
          this.signed = false
        }
      }
      return day;
    },
  },

}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
        id="mc-header"
        class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
            ><span class="return_icon"
              ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t(`events_page.type.9`) }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;" >
      <div
        class="mall-home-top signin-activity-card"
        style="background-image: url('img/activity/14-1.jpg')"
      >
      </div>
      <div class="sigin-content" style="position: absolute">
        <div class="sigin-c-header">
          <div class="am-flexbox am-flexbox-align-middle">
            <div class="am-flexbox-item">
              <span class="sc-score sc-days">{{ res.need_recharge | currency(true,false,true) }}</span><br />
              <div class="sc-score-desc">{{ $t('AGENTCHECK.DATA_NAME_0') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ res.award_money | currency(true,false,true)}}</span><br />
              <div class="sc-score-desc">{{ $t('reward') }}</div>
            </div>
          </div>
        </div>
        <div class="sigin-c-content">
        </div>
         <van-calendar :confirm-disabled-text="$t('confirm-text')" :confirm-text="$t('confirm-text')"
            color="#FFB627"
            :show-title="false"
            :poppable="false"
            :min-date="minDate"
            :max-date="maxDate"
            @select="select"
            :formatter="formatter">
          <template #footer>
            <div style="display: flex;justify-content: center">
              <van-button @click="dailycheckin()" :disabled="signed" block round type="warning">
                {{ selectDay === res.nowday ? $t('events_page.9.BTN_SIGN') : $t('events_page.9.BTN_RESTOCK') }}
              </van-button>
            </div>
          </template>
        </van-calendar>
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b>
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('events_page.9.EXPLAIN_1') }}
                  <br />
                  {{ $t('events_page.9.EXPLAIN_2') }}
                  <br />
                  {{ $t('events_page.9.EXPLAIN_3') }}
                  <br />
                  {{ $t('events_page.9.EXPLAIN_4') }}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.finished {
  background-image: linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}
</style>