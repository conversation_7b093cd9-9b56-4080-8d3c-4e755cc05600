<script>
import {TDTC_ROURE} from "@/api/tdtc";
import Btn0 from '@/modules/mobile/components/popup/ad/Btn0.vue'
import Btn1 from "@/modules/mobile/components/popup/ad/Btn1.vue";
import Btn2 from "@/modules/mobile/components/popup/ad/Btn2.vue";
import Btn3 from "@/modules/mobile/components/popup/ad/Btn3.vue";
import Btn4 from "@/modules/mobile/components/popup/ad/Btn4.vue";
import Btn5 from "@/modules/mobile/components/popup/ad/Btn5.vue";
import Btn6 from "@/modules/mobile/components/popup/ad/Btn6.vue";
import Btn7 from "@/modules/mobile/components/popup/ad/Btn7.vue";
import Btn8 from "@/modules/mobile/components/popup/ad/Btn8.vue";
import Btn9 from "@/modules/mobile/components/popup/ad/Btn9.vue";
import Btn10 from "@/modules/mobile/components/popup/ad/Btn10.vue";
import Btn11 from "@/modules/mobile/components/popup/ad/Btn11.vue";
import Btn12 from "@/modules/mobile/components/popup/ad/Btn12.vue";
import Btn13 from "@/modules/mobile/components/popup/ad/Btn13.vue";
import Btn14 from "@/modules/mobile/components/popup/ad/Btn14.vue";
import Btn15 from "@/modules/mobile/components/popup/ad/Btn15.vue";
import Btn16 from "@/modules/mobile/components/popup/ad/Btn16.vue";
import Btn18 from '@/modules/mobile/components/popup/ad/Btn18.vue'
import Btn19 from '@/modules/mobile/components/popup/ad/Btn19.vue'

export default {
  components: {
    Btn0,
    Btn1,
    Btn2,
    Btn3,
    Btn4,
    Btn5,
    Btn6,
    Btn7,
    Btn8,
    Btn9,
    Btn10,
    Btn11,
    Btn12,
    Btn13,
    Btn14,
    Btn15,
    Btn16,
    Btn18,
    Btn19,
  },

  data() {
    return {
      tabName:     16,
      selectIndex: 0,
      emailIndex:  null,
      emailList:   [],
      ads:         {
        //btn : type
        6:  5,
        1:  6,
        4:  7,
        9:  8,
        7:  10,
        5:  11,
        2:  18,
        8:  38,
        10: 45,
        13: 46,
        12: 200,
        11: 201,
        14: 202,
        15: 203,
        18: 52, // 补助金
        19: 53, // 签到现金奖金
      },
      tabSort:     [
        0,
        16,
        15,
        14,
        9,
        11,
        12,
        13,
        10,
        8,
        18,
        19,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
      ],
    }
  },
  computed: {
    tabFilter() {
      const types = this.$store.state.activitySwitchDetails.map(item => item.activityType);
      return this.tabSort.filter(btn => {
        if (btn === 0) {
          return this.$store.getters.isLogin && !this.$store.state.account.registermobile && this.$store.state.configs.bindphone_web;
        }
        let key = btn - 1;
        let flag = Object.keys(this.ads).includes(key.toString())
        if (flag) {
          return types.includes(this.ads[key])
        } else {
          return true
        }
      })
    }
  },
  methods: {
    email() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_EMAIL)
          .then((res) => {
            // MobileEmailResponse
            iconsole(res)
            if (res['emaile_list']) this.emailList = res['emaile_list']
          })
          .catch(() => {
          })

    },
    readEmail(index) {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_CHECK_EMAIL, {
        'emailid': this.emailList[index]['email_id'],
      })
          .then((res) => {
            this.email()
          })
          .catch(() => {
          })
    }

  },
  watch:   {
    selectIndex: function (n, o) {
      if (n === 1) {
        this.email()
      }
    },
    emailIndex:  function (n, o) {
      this.emailList[n] && !this.emailList[n]['is_check'] && this.readEmail(n)
    }
  }
}
</script>

<template>
  <van-popup :close-on-click-overlay="false" v-model="$store.state.showAd" round class="tdtc-ad-popup">
    <div style="position: fixed; bottom: -1rem; left: 44%" @click="$store.commit('setShowAd', false)">
      <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
    </div>
    <div class="ad-popup-header">
      <div @click="selectIndex = 0" :class="{show: selectIndex === 0}">{{ $t('ad.title') }}
      </div>
      <div @click="selectIndex = 1" :class="{show: selectIndex === 1}">{{ $t('notice.title') }}</div>
    </div>
    <div style="width: 100%;" v-if="selectIndex === 0">
      <van-tabs duration="0.6" v-draggable background="#F1F2F7" :swipeable="true" v-model="tabName" animated line-width="20%" line-height="2px"
          color="#F1F2F7" title-active-color="#FF8200" title-inactive-color="#312E2A" swipe-threshold="1">
        <van-tab v-for="item in tabFilter" :name="item" :key="item">
          <template #title>{{ $t(`ad.tab.${item}`) }} <i v-show="false">{{ item }}</i></template>
          <component :is="`Btn${item}`" :key="item" v-cloak/>
        </van-tab>
      </van-tabs>
    </div>


    <div v-else style="padding: 0 .2rem; text-align: unset; height: 9.6rem; overflow: scroll">
      <template v-if="emailList.length">
        <van-collapse v-model="emailIndex" style="border-radius: 0.1rem;margin-bottom: .08rem;" accordion>
          <van-collapse-item
              style="border-radius: 0.1rem;margin-bottom: .08rem;"
              v-for="(item, index) in emailList"
              center :title="item['title']"
              :key="index"
              :label="$options.filters['datetimeFormat'](item['create_date'])">
            <template #icon>
              <img v-if="item['is_check']" src="/img/tdtc/email/read.png" width="30" alt="" style="margin-right: .3rem;">
              <img v-else src="/img/tdtc/email/unread.png" width="30" alt="" style="margin-right: .3rem;">
            </template>
            {{ item['contents'] }}
          </van-collapse-item>
        </van-collapse>
      </template>
      <van-empty v-else/>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">

::v-deep .record-board {
  margin-top: unset !important;

  .record-board-wrap {
    table {
      tr {
        height: .5rem !important;
        line-height: .5rem !important;
      }
    }
  }
}

::v-deep .van-tab {
  font-size: .24rem;

  &.van-tab--active {
    font-size: .28rem !important;
  }
}

::v-deep .van-tab__pane {
  height: unset;
}

::v-deep .van-tabs__content--animated {
  border-radius: .18rem;
}

::v-deep .van-tabs__wrap--scrollable .van-tabs__nav {
  margin: 0 1px;
}

.tdtc-ad-popup {
  width: 6.66rem;
  height: 10.8rem;
  background: #F1F2F7;
  border-radius: 0.18rem;
  overflow: visible;

  .ad-popup-header {
    margin: 0.4rem .3rem .1rem;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    text-align: center;

    > div {
      width: 2.66rem;
      height: 0.56rem;
      line-height: 0.56rem;
      border-radius: 0.12rem;
      font-size: 0.26rem;
      background: rgba(71, 76, 103, 0);
      border: 0.01px solid rgba(134, 137, 149, 0.99);
      color: #878A96;
    }

    div.show {
      background: #FF8200;
      color: #FFFFFF;
      border: unset;
    }
  }
}
</style>