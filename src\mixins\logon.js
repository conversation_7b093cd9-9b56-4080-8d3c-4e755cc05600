import {
  ROUTE_LOGON_LOGONPHONECAPTCHA,
  ROUTE_LOGON_LOGONUSERNAMECAPTCHA,
  ROUTE_LOGON_REGISTERUSERNAMECAPTCHA,
  ROUTE_LOGON_TOKEN,
  ROUTE_PLATFORM_BALANCE,
  ROUTE_PLATFORM_LOGON,
} from "@/api";
import { MD5 } from "crypto-js";
import { dot } from "@/mixins/dot";
import {play} from '@/mixins/play'
import {TDTC_ROURE} from '@/api/tdtc'

export const logon = {
  mixins: [dot, play],
  data() {
    return {
      retry: 2,
      strength: 0,
      levels: ["#E3E3E3", "#EA4E3D", "#FFAA09", "#007aff", "#04BE02"],
      loginMethod: 0,
    };
  },
  methods: {
    checkPasswordStrength(e) {
      let n = e.length >= 12,
        r = /[a-z]/.test(e),
        i = /[A-Z]/.test(e),
        o = /\d/.test(e),
        a = /[^a-zA-Z0-9]/.test(e),
        u = 0,
        s = [r, i, o, a, n].filter(function (e) {
          return e;
        });
      this.strength = s.length > 1 ? s.length - 1 : 1;
    },
    logonInnit() {
      this.$store.commit(
        "setReferral",
        new URLSearchParams(window.location.search).get("code")
      );

      const token = this.$store.state.token;
      if (token.token) {
        this.$protoApi(ROUTE_LOGON_TOKEN, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: token.token,
        })
          .then((res) => {
            this.$store.commit("setLogon", res);
            this.platformInit();
            this.query46();
          })
          .catch(() => {
            this.platformInit();
          });
      } else {
        this.platformInit();
      }
    },
    platformInit() {
      this.$protoApi(ROUTE_PLATFORM_LOGON, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.$store.commit("setPlatform", res);
          if (this.$store.getters.isLogin) {
            this.leaveThirdGame();
          }
        })
        .catch((res) => {
          if (this.retry) {
            this.retry--;
            let that = this;
            setTimeout(() => {
              that.platformInit();
            }, 1000);
          }
        });
    },
    balanceInit() {
      if (this.$store.state.token.token) {
        this.$protoApi(ROUTE_PLATFORM_BALANCE, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
        })
          .then((res) => {
            this.$store.commit("setBalance", res);
          })
          .catch(() => {});
      }
    },
    captchaRegisterHandler() {
      this.event_registerClick();
      this.$protoApi(ROUTE_LOGON_REGISTERUSERNAMECAPTCHA, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        username: this.user.username,
        passwd: MD5(this.user.password).toString(),
        referral: this.user.referral,
        host: location.hostname.split(".").slice(-2).join(".").toLowerCase(),
        captcha_0: window.validate.captcha_0,
        captcha_1: window.validate.captcha_1,
        captcha_2: window.validate.captcha_2,
        captcha_3: window.validate.captcha_3,
        platformId: this.captchaPlatform,
      })
        .then((res) => {
          this.$store.commit("setLogon", res);
          this.event_register();
          this.platformInit();
            this.query46();
          if (this.$store.state.webType === 1) {
            this.$modal.hide("registerModal");
          } else {
            this.$router.replace("/");
          }
        })
        .catch(() => {
          if (this.$store.state.webType === 1) {
            this.$modal.hide("registerModal");
            setTimeout(() => {
              this.$modal.show("registerModal");
            }, 300);
          }
        });
    },
    captchaLogonHandler() {
      if (!this.loginMethod) {
        this.$protoApi(ROUTE_LOGON_LOGONUSERNAMECAPTCHA, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          username: this.form.username,
          passwd: MD5(this.form.password).toString(),
          oauth: this.form.code,
          captcha_0: window.validate.captcha_0,
          captcha_1: window.validate.captcha_1,
          captcha_2: window.validate.captcha_2,
          captcha_3: window.validate.captcha_3,
          platformId: this.captchaPlatform,
        })
            .then((res) => {
              this.$store.commit("setLogon", res);
              this.event_login(this.$store.state.account.userId);
              this.platformInit();
              this.query46();
              if (this.$store.state.webType === 1) {
                this.$modal.hide("loginPopupModal");
              } else {
                this.$router.replace("/").catch(()=>{});
              }
            })
            .catch((res) => {
              if (res.code === 516) {
                this.oauth = true;
              }
            });
      } else {
        this.$protoApi(ROUTE_LOGON_LOGONPHONECAPTCHA, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          phone: this.$store.state.phonePreApi + this.form.phone,
          passwd: MD5(this.form.password).toString(),
          oauth: this.form.code,
          captcha_0: window.validate.captcha_0,
          captcha_1: window.validate.captcha_1,
          captcha_2: window.validate.captcha_2,
          captcha_3: window.validate.captcha_3,
          platformId: this.captchaPlatform,
        })
            .then((res) => {
              this.$store.commit("setLogon", res);
              this.event_login(this.$store.state.account.userId);
              this.platformInit();
              this.query46();
              if (this.$store.state.webType === 1) {
                this.$modal.hide("loginPopupModal");
              } else {
                this.$router.replace("/").catch(()=>{});
              }
            })
            .catch((res) => {
              if (res.code === 516) {
                this.oauth = true;
              }
            });
      }
    },
    query46() {
      this.$tdtcApi
          .getQueryInfo(TDTC_ROURE.QUERY_ACTIVE_POPUP)
          .then((res) => {

          })
          .catch(() => {});
    },
  },
};
